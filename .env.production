# Production Environment Variables
# Copy this to .env and fill in the actual values

# Database Configuration (PostgreSQL)
DATABASE_URL=postgresql://username:password@host:port/database_name

# Security (CRITICAL - Generate strong keys for production)
SECRET_KEY=your-super-secret-key-here-minimum-32-characters-long
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OpenAI Configuration (REQUIRED)
OPENAI_API_KEY=your-openai-api-key-here

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-secure-admin-password-here

# File Upload Configuration
MAX_FILE_SIZE_MB=50
ALLOWED_AUDIO_FORMATS=mp3,wav,m4a,flac
UPLOAD_DIR=uploads

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
PORT=8000

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# CORS Origins (comma-separated)
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
