title,content,difficulty_level,question_type,expected_duration_minutes,scoring_criteria,sample_answer,keywords,categories
"Tell me about yourself","Please provide a brief introduction about yourself, your background, and what brings you to this interview.",easy,behavioral,3,"Clarity of communication, relevance to role, professional presentation","I'm a software engineer with 5 years of experience in web development. I specialize in Python and JavaScript, and I'm passionate about creating user-friendly applications. I'm here because I'm excited about the opportunity to contribute to your team's innovative projects.","introduction, background, experience, motivation",General
"Describe a challenging project","Tell me about a challenging project you worked on and how you overcame the difficulties.",medium,behavioral,5,"Problem-solving approach, specific examples, lessons learned, impact","I worked on a project to migrate our legacy system to a microservices architecture. The main challenge was ensuring zero downtime during migration. I developed a phased approach, implemented feature flags, and coordinated with multiple teams. We successfully completed the migration with 99.9% uptime.","project management, problem solving, teamwork, technical challenges",Technical
"Where do you see yourself in 5 years","Describe your career goals and where you see yourself professionally in the next 5 years.",easy,behavioral,3,"Realistic goals, alignment with role, growth mindset","In 5 years, I see myself as a senior technical lead, mentoring junior developers and driving architectural decisions. I want to deepen my expertise in cloud technologies and contribute to open-source projects. I'm also interested in developing my leadership skills.","career goals, growth, leadership, future plans",General
"Explain a technical concept","Explain a complex technical concept to someone without a technical background.",medium,technical,4,"Clarity of explanation, use of analogies, audience awareness","I'll explain APIs using a restaurant analogy. An API is like a waiter in a restaurant. You (the customer) don't go directly to the kitchen to get your food. Instead, you tell the waiter what you want, the waiter communicates with the kitchen, and brings back your order. Similarly, an API acts as an intermediary between different software applications.","communication, technical knowledge, analogies, simplification",Technical
"Handle a difficult team member","How would you handle a situation where a team member is not contributing effectively?",medium,situational,4,"Conflict resolution, leadership approach, empathy, practical solutions","I would first try to understand the root cause by having a private conversation with the team member. There might be personal issues, unclear expectations, or skill gaps. Based on the situation, I'd provide support, clarify expectations, or arrange additional training. If the issue persists, I'd involve management while documenting the situation.","leadership, conflict resolution, team management, communication",Management
"Debugging a production issue","Walk me through how you would debug a critical production issue.",hard,technical,6,"Systematic approach, prioritization, communication, technical depth","First, I'd assess the impact and communicate with stakeholders. Then I'd gather information: error logs, monitoring data, recent deployments. I'd isolate the issue by checking each system component systematically. Once identified, I'd implement a fix, test it thoroughly, and deploy with proper rollback plans. Finally, I'd conduct a post-mortem to prevent recurrence.","debugging, problem solving, system thinking, incident management",Technical
"Prioritizing multiple deadlines","How do you prioritize when you have multiple urgent deadlines?",medium,situational,4,"Time management, decision-making, stakeholder communication","I start by listing all tasks and their true deadlines, then assess the impact of delays for each. I communicate with stakeholders to understand priorities and negotiate timelines if needed. I break large tasks into smaller chunks and focus on high-impact items first. I also identify tasks that can be delegated or simplified.","time management, prioritization, communication, organization",General
"Learning new technology","Describe a time when you had to quickly learn a new technology or skill.",medium,behavioral,4,"Learning agility, resourcefulness, application of knowledge","When our team decided to adopt React, I had only Angular experience. I started with official documentation and online tutorials, built small practice projects, and joined developer communities. Within two weeks, I was contributing to our React codebase. I also shared my learning with teammates through internal presentations.","learning, adaptability, self-motivation, knowledge sharing",Technical
"Handling failure","Tell me about a time you failed and what you learned from it.",medium,behavioral,4,"Self-awareness, accountability, growth mindset, lessons learned","I once deployed code without proper testing that caused a service outage. I immediately took responsibility, worked with the team to restore service, and implemented additional testing procedures. I learned the importance of thorough testing and now always use staging environments that mirror production.","accountability, learning, problem solving, improvement",General
"System design question","How would you design a URL shortening service like bit.ly?",hard,technical,8,"System thinking, scalability, trade-offs, technical depth","I'd use a hash-based approach with a database to store URL mappings. For scalability, I'd implement caching with Redis, use load balancers, and consider database sharding. For the hash function, I'd use base62 encoding. I'd also implement rate limiting, analytics tracking, and consider CDN for global distribution.","system design, scalability, databases, architecture",Technical
