# 🚀 AI Interview API - Render Deployment Guide

## Overview

Render is a modern cloud platform that offers easy deployment with automatic builds, free SSL, and integrated databases. Your AI Interview API is pre-configured for Render deployment.

## Prerequisites

1. **Render Account**: Sign up at [render.com](https://render.com)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **OpenAI API Key**: Get one from [OpenAI Platform](https://platform.openai.com/api-keys)

## Deployment Methods

### Method 1: One-Click Deploy (Recommended)

1. **Fork this repository** to your GitHub account

2. **Click the Deploy to Render button** (if available):
   [![Deploy to Render](https://render.com/images/deploy-to-render-button.svg)](https://render.com/deploy)

3. **Connect your GitHub account** to Render

4. **Select your forked repository**

5. **Ren<PERSON> will automatically**:
   - Detect the `render.yaml` configuration
   - Create the web service and PostgreSQL database
   - Generate secure values for `SECRET_KEY` and `ADMIN_PASSWORD`
   - Start the deployment process

6. **Set your OpenAI API Key**:
   - Go to your service dashboard
   - Navigate to "Environment" tab
   - Set `OPENAI_API_KEY` to your OpenAI API key
   - Save and redeploy

### Method 2: Manual Deployment

#### Step 1: Create Web Service

1. **Login to Render** and go to your dashboard

2. **Click "New +"** and select "Web Service"

3. **Connect your repository**:
   - Choose "Build and deploy from a Git repository"
   - Connect your GitHub account
   - Select your AI Interview API repository

4. **Configure the service**:
   - **Name**: `ai-interview-api`
   - **Region**: Choose closest to your users (Oregon, Frankfurt, Singapore)
   - **Branch**: `main`
   - **Runtime**: `Python 3`
   - **Build Command**: `pip install -r requirements-prod.txt`
   - **Start Command**: `uvicorn app.main_prod:app --host 0.0.0.0 --port $PORT --workers 1`

#### Step 2: Create PostgreSQL Database

1. **Click "New +"** and select "PostgreSQL"

2. **Configure database**:
   - **Name**: `ai-interview-db`
   - **Database Name**: `ai_interview_api`
   - **User**: `ai_interview_user`
   - **Region**: Same as your web service
   - **Plan**: Free

3. **Note the connection details** (Render will provide these)

#### Step 3: Configure Environment Variables

In your web service dashboard, go to "Environment" and add:

```bash
# Required Variables
SECRET_KEY=<auto-generated-by-render>
OPENAI_API_KEY=sk-your-openai-api-key-here
ADMIN_PASSWORD=<auto-generated-by-render>
DATABASE_URL=<auto-set-by-render>

# Optional Variables
DEBUG=false
LOG_LEVEL=INFO
ADMIN_EMAIL=<EMAIL>
MAX_FILE_SIZE_MB=50
ALLOWED_AUDIO_FORMATS=mp3,wav,m4a,flac
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

#### Step 4: Deploy

1. **Click "Create Web Service"**
2. **Render will automatically**:
   - Build your application
   - Deploy to a global CDN
   - Provide HTTPS automatically
   - Give you a public URL

## Configuration Details

### render.yaml Explanation

Your project includes a `render.yaml` file that automates the entire deployment:

```yaml
services:
  - type: web
    name: ai-interview-api
    env: python
    plan: free
    region: oregon
    buildCommand: pip install -r requirements-prod.txt
    startCommand: uvicorn app.main_prod:app --host 0.0.0.0 --port $PORT --workers 1
    healthCheckPath: /health
    autoDeploy: true

databases:
  - name: ai-interview-db
    plan: free
    databaseName: ai_interview_api
    user: ai_interview_user
```

### Environment Variables Reference

| Variable | Description | Required | Auto-Generated |
|----------|-------------|----------|----------------|
| `SECRET_KEY` | JWT signing key | ✅ | ✅ |
| `OPENAI_API_KEY` | OpenAI API key | ✅ | ❌ |
| `ADMIN_PASSWORD` | Admin password | ✅ | ✅ |
| `DATABASE_URL` | PostgreSQL URL | ✅ | ✅ |
| `DEBUG` | Debug mode | ❌ | ❌ |
| `LOG_LEVEL` | Logging level | ❌ | ❌ |

## Post-Deployment

### 1. Verify Deployment

Your app will be available at: `https://your-app-name.onrender.com`

Test these endpoints:
```bash
# Health check
curl https://your-app-name.onrender.com/health

# API info
curl https://your-app-name.onrender.com/

# API documentation (if DEBUG=true)
open https://your-app-name.onrender.com/docs
```

### 2. Admin Access

- **Username**: `admin`
- **Password**: Check the `ADMIN_PASSWORD` environment variable in Render dashboard
- **Login URL**: `https://your-app-name.onrender.com/api/v1/auth/login`

### 3. Database Access

Render provides database connection details in your service dashboard:
- **Host**: Provided by Render
- **Port**: 5432
- **Database**: `ai_interview_api`
- **Username**: `ai_interview_user`
- **Password**: Auto-generated

## Render Features

### ✅ **Included with Free Plan**:
- 750 hours/month of runtime
- Automatic HTTPS/SSL
- Global CDN
- Automatic deployments from Git
- 100GB bandwidth/month
- PostgreSQL database (90 days retention)

### 🔄 **Automatic Features**:
- **Auto-deploy**: Pushes to main branch trigger deployments
- **Health checks**: Automatic monitoring via `/health` endpoint
- **SSL certificates**: Automatic HTTPS setup
- **Environment isolation**: Secure environment variable management

### 📊 **Monitoring**:
- Real-time logs in dashboard
- Performance metrics
- Uptime monitoring
- Error tracking

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check build logs in Render dashboard
   - Verify `requirements-prod.txt` dependencies
   - Ensure Python version compatibility

2. **Database Connection Errors**
   - Verify `DATABASE_URL` is set correctly
   - Check database service status
   - Ensure database and web service are in same region

3. **OpenAI API Errors**
   - Verify `OPENAI_API_KEY` is set correctly
   - Check API key permissions and quota
   - Monitor API usage in OpenAI dashboard

4. **Application Crashes**
   - Check logs in Render dashboard
   - Verify all required environment variables are set
   - Check health endpoint response

### Debugging Commands

```bash
# View logs (in Render dashboard)
# Go to your service > Logs tab

# Check environment variables
# Go to your service > Environment tab

# Manual health check
curl https://your-app-name.onrender.com/health

# Database connection test
# Use Render's built-in database shell
```

## Scaling and Performance

### Free Tier Limitations
- Service sleeps after 15 minutes of inactivity
- 750 hours/month runtime limit
- Single instance only

### Upgrading Options
- **Starter Plan ($7/month)**: No sleep, faster builds
- **Standard Plan ($25/month)**: More resources, multiple instances
- **Pro Plan ($85/month)**: Advanced features, priority support

## Security Best Practices

- ✅ HTTPS enabled by default
- ✅ Environment variables encrypted
- ✅ Auto-generated secure passwords
- ✅ Database encryption at rest
- ✅ Network isolation
- ✅ Regular security updates

## Support and Resources

- 📖 **Render Docs**: https://render.com/docs
- 💬 **Community**: https://community.render.com
- 🆘 **Support**: https://render.com/support
- 📚 **API Docs**: https://your-app-name.onrender.com/docs
